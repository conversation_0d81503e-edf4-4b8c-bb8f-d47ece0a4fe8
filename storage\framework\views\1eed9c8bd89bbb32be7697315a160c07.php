<span class="product-price single-price-display">
    <?php if($product->front_sale_price !== $product->price): ?>
        <span class="product-price-sale d-flex align-items-center">
            <ins class="current-price">
                <span class="price-amount">
                    <bdi>
                        <span class="amount"><?php echo e(format_price($product->front_sale_price_with_taxes)); ?></span>
                    </bdi>
                </span>
            </ins>
            <del class="original-price" aria-hidden="true">
                <span class="price-amount">
                    <bdi>
                        <span class="amount"><?php echo e(format_price($product->price_with_taxes)); ?></span>
                    </bdi>
                </span>
            </del>
        </span>
    <?php else: ?>
        <span class="product-price-original">
            <span class="price-amount">
                <bdi>
                    <span class="amount"><?php echo e(format_price($product->front_sale_price_with_taxes)); ?></span>
                </bdi>
            </span>
        </span>
    <?php endif; ?>
</span>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/ecommerce/product-price.blade.php ENDPATH**/ ?>